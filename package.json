{"name": "caparan-tin<PERSON>an", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "node prisma/seed.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "autoprefixer": "^10.4.21", "cloudinary": "^2.7.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.523.0", "next": "15.3.4", "next-cloudinary": "^6.16.0", "postcss": "^8.5.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwindcss": "^3.4.17", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "typescript": "^5"}}