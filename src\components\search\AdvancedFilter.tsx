'use client'

import { useState, useEffect } from 'react'
import { Filter, X, ChevronDown } from 'lucide-react'

interface FilterOption {
  label: string
  value: string
  count?: number
}

interface FilterConfig {
  key: string
  label: string
  type: 'select' | 'multiselect' | 'range' | 'date' | 'search'
  options?: FilterOption[]
  min?: number
  max?: number
  icon?: React.ComponentType<any>
}

interface ActiveFilter {
  key: string
  label: string
  value: any
  displayValue: string
}

interface AdvancedFilterProps {
  filters: FilterConfig[]
  onFiltersChange: (filters: Record<string, any>) => void
  className?: string
  showActiveFilters?: boolean
}

export default function AdvancedFilter({
  filters,
  onFiltersChange,
  className = "",
  showActiveFilters = true
}: AdvancedFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [tempFilters, setTempFilters] = useState<Record<string, any>>({})

  useEffect(() => {
    setTempFilters(activeFilters)
  }, [activeFilters])

  const handleFilterChange = (key: string, value: any) => {
    setTempFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const applyFilters = () => {
    setActiveFilters(tempFilters)
    onFiltersChange(tempFilters)
    setIsOpen(false)
  }

  const clearFilters = () => {
    setActiveFilters({})
    setTempFilters({})
    onFiltersChange({})
    setIsOpen(false)
  }

  const removeFilter = (key: string) => {
    const updated = { ...activeFilters }
    delete updated[key]
    setActiveFilters(updated)
    setTempFilters(updated)
    onFiltersChange(updated)
  }

  const getActiveFiltersList = (): ActiveFilter[] => {
    return Object.entries(activeFilters)
      .filter(([, value]) => value !== undefined && value !== '' && value !== null)
      .map(([key, value]) => {
        const filterConfig = filters.find(f => f.key === key)
        if (!filterConfig) return null

        let displayValue = ''
        
        if (filterConfig.type === 'select' || filterConfig.type === 'multiselect') {
          if (Array.isArray(value)) {
            const selectedOptions = filterConfig.options?.filter(opt => value.includes(opt.value))
            displayValue = selectedOptions?.map(opt => opt.label).join(', ') || ''
          } else {
            const selectedOption = filterConfig.options?.find(opt => opt.value === value)
            displayValue = selectedOption?.label || value
          }
        } else if (filterConfig.type === 'range') {
          displayValue = `${value.min} - ${value.max}`
        } else if (filterConfig.type === 'date') {
          if (value.start && value.end) {
            displayValue = `${value.start} to ${value.end}`
          } else if (value.start) {
            displayValue = `From ${value.start}`
          } else if (value.end) {
            displayValue = `Until ${value.end}`
          }
        } else {
          displayValue = value.toString()
        }

        return {
          key,
          label: filterConfig.label,
          value,
          displayValue
        }
      })
      .filter(Boolean) as ActiveFilter[]
  }

  const activeFiltersList = getActiveFiltersList()
  const hasActiveFilters = activeFiltersList.length > 0

  const renderFilterInput = (filter: FilterConfig) => {
    const value = tempFilters[filter.key]

    switch (filter.type) {
      case 'select':
        return (
          <select
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All {filter.label}</option>
            {filter.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label} {option.count && `(${option.count})`}
              </option>
            ))}
          </select>
        )

      case 'multiselect':
        return (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {filter.options?.map(option => (
              <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={Array.isArray(value) ? value.includes(option.value) : false}
                  onChange={(e) => {
                    const currentValues = Array.isArray(value) ? value : []
                    if (e.target.checked) {
                      handleFilterChange(filter.key, [...currentValues, option.value])
                    } else {
                      handleFilterChange(filter.key, currentValues.filter(v => v !== option.value))
                    }
                  }}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-body-medium text-gray-700">
                  {option.label} {option.count && `(${option.count})`}
                </span>
              </label>
            ))}
          </div>
        )

      case 'range':
        return (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-label-medium text-gray-700 mb-1 block">Min</label>
              <input
                type="number"
                value={value?.min || ''}
                onChange={(e) => handleFilterChange(filter.key, {
                  ...value,
                  min: e.target.value ? Number(e.target.value) : undefined
                })}
                min={filter.min}
                max={filter.max}
                placeholder="Min"
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <div>
              <label className="text-label-medium text-gray-700 mb-1 block">Max</label>
              <input
                type="number"
                value={value?.max || ''}
                onChange={(e) => handleFilterChange(filter.key, {
                  ...value,
                  max: e.target.value ? Number(e.target.value) : undefined
                })}
                min={filter.min}
                max={filter.max}
                placeholder="Max"
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>
        )

      case 'date':
        return (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-label-medium text-gray-700 mb-1 block">From</label>
              <input
                type="date"
                value={value?.start || ''}
                onChange={(e) => handleFilterChange(filter.key, {
                  ...value,
                  start: e.target.value
                })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <div>
              <label className="text-label-medium text-gray-700 mb-1 block">To</label>
              <input
                type="date"
                value={value?.end || ''}
                onChange={(e) => handleFilterChange(filter.key, {
                  ...value,
                  end: e.target.value
                })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>
        )

      case 'search':
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.key, e.target.value)}
            placeholder={`Search ${filter.label.toLowerCase()}...`}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        )

      default:
        return null
    }
  }

  return (
    <div className={className}>
      {/* Filter Button */}
      <div className="flex items-center space-x-3">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            flex items-center space-x-2 px-4 py-2 border rounded-lg transition-all duration-200
            ${hasActiveFilters
              ? 'border-primary bg-primary-light text-primary'
              : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
            }
          `}
        >
          <Filter className="h-4 w-4" />
          <span className="text-body-medium font-medium">
            Filters {hasActiveFilters && `(${activeFiltersList.length})`}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-body-small text-gray-500 hover:text-gray-700 transition-colors"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Active Filters */}
      {showActiveFilters && hasActiveFilters && (
        <div className="mt-3 flex flex-wrap gap-2">
          {activeFiltersList.map((filter) => (
            <div
              key={filter.key}
              className="inline-flex items-center space-x-2 bg-primary-light text-primary px-3 py-1 rounded-full text-body-small"
            >
              <span>{filter.label}: {filter.displayValue}</span>
              <button
                onClick={() => removeFilter(filter.key)}
                className="hover:text-primary-dark transition-colors"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Filter Panel */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 p-6 animate-fade-in">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filters.map((filter) => {
              const IconComponent = filter.icon
              return (
                <div key={filter.key} className="space-y-3">
                  <label className="flex items-center space-x-2 text-label-large text-gray-900">
                    {IconComponent && <IconComponent className="h-4 w-4" />}
                    <span>{filter.label}</span>
                  </label>
                  {renderFilterInput(filter)}
                </div>
              )
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => setIsOpen(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={clearFilters}
              className="btn-secondary"
            >
              Clear All
            </button>
            <button
              onClick={applyFilters}
              className="btn-primary"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
