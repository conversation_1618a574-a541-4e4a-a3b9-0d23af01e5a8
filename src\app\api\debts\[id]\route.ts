import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/debts/[id] - Get a specific debt
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const debt = await prisma.customerDebt.findUnique({
      where: { id },
      include: {
        customer: true,
        product: true,
        payments: {
          orderBy: { dateOfPayment: 'desc' },
        },
      },
    })

    if (!debt) {
      return NextResponse.json(
        { error: 'Debt not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(debt)
  } catch (error) {
    console.error('Error fetching debt:', error)
    return NextResponse.json(
      { error: 'Failed to fetch debt' },
      { status: 500 }
    )
  }
}

// PUT /api/debts/[id] - Update a debt (mainly for marking as paid)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { isPaid } = body

    const debt = await prisma.customerDebt.update({
      where: { id },
      data: { isPaid },
      include: {
        customer: true,
        product: true,
      },
    })

    return NextResponse.json(debt)
  } catch (error) {
    console.error('Error updating debt:', error)
    return NextResponse.json(
      { error: 'Failed to update debt' },
      { status: 500 }
    )
  }
}

// DELETE /api/debts/[id] - Delete a debt
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Get the debt details first to restore stock
    const debt = await prisma.customerDebt.findUnique({
      where: { id },
    })

    if (!debt) {
      return NextResponse.json(
        { error: 'Debt not found' },
        { status: 404 }
      )
    }

    // Delete debt and restore stock in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete the debt
      await tx.customerDebt.delete({
        where: { id },
      })

      // Restore product stock
      await tx.product.update({
        where: { id: debt.productId },
        data: {
          stock: {
            increment: debt.quantity,
          },
        },
      })
    })

    return NextResponse.json({ message: 'Debt deleted successfully' })
  } catch (error) {
    console.error('Error deleting debt:', error)
    return NextResponse.json(
      { error: 'Failed to delete debt' },
      { status: 500 }
    )
  }
}
