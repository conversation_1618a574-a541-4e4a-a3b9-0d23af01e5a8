'use client'

import { useState, useEffect } from 'react'
import { X, CheckCircle, AlertTriangle, XCircle, Info } from 'lucide-react'

interface NotificationProps {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  duration?: number
  onClose: (id: string) => void
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

export default function AnimatedNotification({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose,
  position = 'top-right'
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Entrance animation
    const showTimer = setTimeout(() => setIsVisible(true), 100)
    
    // Auto-dismiss
    const hideTimer = setTimeout(() => {
      handleClose()
    }, duration)

    return () => {
      clearTimeout(showTimer)
      clearTimeout(hideTimer)
    }
  }, [duration])

  const handleClose = () => {
    setIsExiting(true)
    setTimeout(() => {
      onClose(id)
    }, 300)
  }

  const typeConfig = {
    success: {
      icon: CheckCircle,
      bgColor: 'bg-success-light',
      borderColor: 'border-success-500',
      iconColor: 'text-success-600',
      titleColor: 'text-success-800'
    },
    warning: {
      icon: AlertTriangle,
      bgColor: 'bg-warning-light',
      borderColor: 'border-warning-500',
      iconColor: 'text-warning-600',
      titleColor: 'text-warning-800'
    },
    error: {
      icon: XCircle,
      bgColor: 'bg-error-light',
      borderColor: 'border-error-500',
      iconColor: 'text-error-600',
      titleColor: 'text-error-800'
    },
    info: {
      icon: Info,
      bgColor: 'bg-primary-light',
      borderColor: 'border-primary-500',
      iconColor: 'text-primary-600',
      titleColor: 'text-primary-800'
    }
  }

  const config = typeConfig[type]
  const Icon = config.icon

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  }

  const animationClasses = {
    'top-right': isVisible && !isExiting ? 'animate-slide-in-right' : 'translate-x-full opacity-0',
    'top-left': isVisible && !isExiting ? 'animate-slide-in-left' : '-translate-x-full opacity-0',
    'bottom-right': isVisible && !isExiting ? 'animate-slide-in-right' : 'translate-x-full opacity-0',
    'bottom-left': isVisible && !isExiting ? 'animate-slide-in-left' : '-translate-x-full opacity-0'
  }

  return (
    <div
      className={`
        fixed ${positionClasses[position]} z-50
        max-w-sm w-full mx-4
        transform transition-all duration-300 ease-out
        ${animationClasses[position]}
        ${isExiting ? 'translate-x-full opacity-0' : ''}
      `}
    >
      <div
        className={`
          ${config.bgColor} ${config.borderColor}
          border-l-4 rounded-lg shadow-lg p-4
          hover-lift
        `}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={`h-5 w-5 ${config.iconColor} animate-scale-in`} />
          </div>
          <div className="ml-3 flex-1">
            <h3 className={`text-sm font-semibold ${config.titleColor}`}>
              {title}
            </h3>
            <p className="text-sm text-gray-700 mt-1">
              {message}
            </p>
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleClose}
              className="
                inline-flex text-gray-400 hover:text-gray-600
                focus:outline-none focus:text-gray-600
                transition-colors duration-200
                hover-scale
              "
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 w-full bg-gray-200 rounded-full h-1">
          <div
            className={`
              h-1 rounded-full transition-all ease-linear
              ${config.borderColor.replace('border-', 'bg-')}
            `}
            style={{
              width: '100%',
              animation: `shrink ${duration}ms linear forwards`
            }}
          />
        </div>
      </div>
    </div>
  )
}

// Notification Manager Hook
export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationProps[]>([])

  const addNotification = (notification: Omit<NotificationProps, 'id' | 'onClose'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setNotifications(prev => [...prev, { ...notification, id, onClose: removeNotification }])
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message })
  }

  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message })
  }

  const showWarning = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message })
  }

  const showInfo = (title: string, message: string) => {
    addNotification({ type: 'info', title, message })
  }

  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification
  }
}

// Notification Container Component
export function NotificationContainer() {
  const { notifications } = useNotifications()

  return (
    <>
      {notifications.map(notification => (
        <AnimatedNotification key={notification.id} {...notification} />
      ))}
    </>
  )
}

// Add CSS for progress bar animation
const progressBarCSS = `
@keyframes shrink {
  from { width: 100%; }
  to { width: 0%; }
}
`

// Inject CSS
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = progressBarCSS
  document.head.appendChild(style)
}
