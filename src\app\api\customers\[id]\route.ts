import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { customerSchema } from '@/lib/validations'

// GET /api/customers/[id] - Get a specific customer
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        debts: {
          include: {
            product: true,
            payments: true,
          },
          orderBy: { dateOfDebt: 'desc' },
        },
        payments: {
          orderBy: { dateOfPayment: 'desc' },
        },
      },
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Calculate debt summary
    const totalDebt = customer.debts.reduce((sum, debt) => sum + debt.totalAmount, 0)
    const totalPaid = customer.payments.reduce((sum, payment) => sum + payment.amount, 0)
    const remainingDebt = totalDebt - totalPaid

    return NextResponse.json({
      ...customer,
      totalDebt,
      totalPaid,
      remainingDebt,
    })
  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    )
  }
}

// PUT /api/customers/[id] - Update a customer
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const validatedData = customerSchema.parse(body)

    const customer = await prisma.customer.update({
      where: { id },
      data: validatedData,
    })

    return NextResponse.json(customer)
  } catch (error) {
    console.error('Error updating customer:', error)
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid customer data', details: error },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    )
  }
}

// DELETE /api/customers/[id] - Delete a customer
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    await prisma.customer.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    )
  }
}
