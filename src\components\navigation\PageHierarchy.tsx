'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home, Folder, File } from 'lucide-react'

interface HierarchyNode {
  label: string
  path: string
  icon?: React.ComponentType<any>
  children?: HierarchyNode[]
  isActive?: boolean
}

interface PageHierarchyProps {
  className?: string
  showIcons?: boolean
  maxDepth?: number
}

export default function PageHierarchy({
  className = "",
  showIcons = true,
  maxDepth = 5
}: PageHierarchyProps) {
  const pathname = usePathname()

  // Define the application hierarchy
  const hierarchy: HierarchyNode = {
    label: 'Caparan Tindahan',
    path: '/dashboard',
    icon: Home,
    children: [
      {
        label: 'Dashboard',
        path: '/dashboard',
        icon: Home
      },
      {
        label: 'Analytics',
        path: '/analytics',
        icon: Home
      },
      {
        label: 'Inventory',
        path: '/products',
        icon: Folder,
        children: [
          {
            label: 'Products',
            path: '/products',
            icon: File
          },
          {
            label: 'Add Product',
            path: '/products/new',
            icon: File
          },
          {
            label: 'Categories',
            path: '/products/categories',
            icon: File
          }
        ]
      },
      {
        label: 'Customer Management',
        path: '/customers',
        icon: Folder,
        children: [
          {
            label: 'Customers',
            path: '/customers',
            icon: File
          },
          {
            label: 'Add Customer',
            path: '/customers/new',
            icon: File
          }
        ]
      },
      {
        label: 'Financial',
        path: '/debts',
        icon: Folder,
        children: [
          {
            label: 'Debts',
            path: '/debts',
            icon: File
          },
          {
            label: 'Payments',
            path: '/payments',
            icon: File
          },
          {
            label: 'Record Debt',
            path: '/debts/new',
            icon: File
          },
          {
            label: 'Record Payment',
            path: '/payments/new',
            icon: File
          }
        ]
      },
      {
        label: 'Reports',
        path: '/reports',
        icon: Folder,
        children: [
          {
            label: 'Sales Reports',
            path: '/reports/sales',
            icon: File
          },
          {
            label: 'Inventory Reports',
            path: '/reports/inventory',
            icon: File
          },
          {
            label: 'Financial Reports',
            path: '/reports/financial',
            icon: File
          }
        ]
      },
      {
        label: 'Settings',
        path: '/settings',
        icon: File
      }
    ]
  }

  // Find the current page in the hierarchy
  const findCurrentPath = (node: HierarchyNode, currentPath: string, path: HierarchyNode[] = []): HierarchyNode[] | null => {
    const currentNodePath = [...path, node]
    
    if (node.path === currentPath) {
      return currentNodePath
    }
    
    if (node.children) {
      for (const child of node.children) {
        const result = findCurrentPath(child, currentPath, currentNodePath)
        if (result) {
          return result
        }
      }
    }
    
    return null
  }

  const currentPath = findCurrentPath(hierarchy, pathname)
  
  if (!currentPath || currentPath.length === 0) {
    return null
  }

  // Limit depth if specified
  const displayPath = currentPath.slice(0, maxDepth)

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="px-6 py-3">
        <nav className="flex items-center space-x-1 text-sm" aria-label="Page hierarchy">
          <ol className="flex items-center space-x-1">
            {displayPath.map((node, index) => {
              const isLast = index === displayPath.length - 1
              const IconComponent = showIcons ? node.icon : null

              return (
                <li key={node.path} className="flex items-center">
                  {index > 0 && (
                    <ChevronRight className="h-4 w-4 text-gray-400 mx-2 flex-shrink-0" />
                  )}
                  
                  {isLast ? (
                    <span className="flex items-center space-x-1.5 text-gray-900 font-medium">
                      {IconComponent && (
                        <IconComponent className="h-4 w-4 text-gray-600" />
                      )}
                      <span>{node.label}</span>
                    </span>
                  ) : (
                    <Link
                      href={node.path}
                      className="flex items-center space-x-1.5 text-gray-500 hover:text-gray-700 transition-colors duration-200 rounded-md px-2 py-1 hover:bg-gray-100"
                    >
                      {IconComponent && (
                        <IconComponent className="h-4 w-4" />
                      )}
                      <span>{node.label}</span>
                    </Link>
                  )}
                </li>
              )
            })}
          </ol>
        </nav>
      </div>
    </div>
  )
}

// Sidebar hierarchy component
export function SidebarHierarchy() {
  const pathname = usePathname()

  const menuItems = [
    {
      label: 'Overview',
      items: [
        { label: 'Dashboard', path: '/dashboard', icon: Home },
        { label: 'Analytics', path: '/analytics', icon: Home }
      ]
    },
    {
      label: 'Inventory Management',
      items: [
        { label: 'Products', path: '/products', icon: File },
        { label: 'Categories', path: '/products/categories', icon: File }
      ]
    },
    {
      label: 'Customer Management',
      items: [
        { label: 'Customers', path: '/customers', icon: File },
        { label: 'Customer Groups', path: '/customers/groups', icon: File }
      ]
    },
    {
      label: 'Financial Management',
      items: [
        { label: 'Debts', path: '/debts', icon: File },
        { label: 'Payments', path: '/payments', icon: File }
      ]
    },
    {
      label: 'Reports & Analytics',
      items: [
        { label: 'Sales Reports', path: '/reports/sales', icon: File },
        { label: 'Inventory Reports', path: '/reports/inventory', icon: File },
        { label: 'Financial Reports', path: '/reports/financial', icon: File }
      ]
    }
  ]

  return (
    <div className="space-y-6">
      {menuItems.map((section, sectionIndex) => (
        <div key={sectionIndex}>
          <h3 className="text-label-medium text-gray-500 uppercase tracking-wider mb-3">
            {section.label}
          </h3>
          <ul className="space-y-1">
            {section.items.map((item) => {
              const isActive = pathname === item.path
              const IconComponent = item.icon

              return (
                <li key={item.path}>
                  <Link
                    href={item.path}
                    className={`
                      flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors duration-200
                      ${isActive
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <IconComponent className="h-4 w-4 flex-shrink-0" />
                    <span className="text-body-medium">{item.label}</span>
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>
      ))}
    </div>
  )
}

// Context-aware page navigation
export function ContextNavigation() {
  const pathname = usePathname()

  // Define related pages for each section
  const relatedPages: Record<string, Array<{ label: string; path: string; description: string }>> = {
    '/products': [
      { label: 'Add Product', path: '/products/new', description: 'Add a new product to inventory' },
      { label: 'Categories', path: '/products/categories', description: 'Manage product categories' },
      { label: 'Inventory Report', path: '/reports/inventory', description: 'View inventory analytics' }
    ],
    '/customers': [
      { label: 'Add Customer', path: '/customers/new', description: 'Register a new customer' },
      { label: 'Customer Debts', path: '/debts', description: 'View customer debt records' },
      { label: 'Customer Reports', path: '/reports/customers', description: 'Customer analytics' }
    ],
    '/debts': [
      { label: 'Record Debt', path: '/debts/new', description: 'Record a new customer debt' },
      { label: 'Payments', path: '/payments', description: 'View payment records' },
      { label: 'Customers', path: '/customers', description: 'Manage customers' }
    ],
    '/payments': [
      { label: 'Record Payment', path: '/payments/new', description: 'Record a new payment' },
      { label: 'Debts', path: '/debts', description: 'View outstanding debts' },
      { label: 'Financial Reports', path: '/reports/financial', description: 'Financial analytics' }
    ]
  }

  const currentRelated = relatedPages[pathname]

  if (!currentRelated || currentRelated.length === 0) {
    return null
  }

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <h3 className="text-heading-small text-gray-900 mb-3">Related Actions</h3>
      <div className="space-y-2">
        {currentRelated.map((item) => (
          <Link
            key={item.path}
            href={item.path}
            className="block p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-body-medium font-medium text-gray-900">{item.label}</h4>
                <p className="text-body-small text-gray-500 mt-1">{item.description}</p>
              </div>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </div>
          </Link>
        ))}
      </div>
    </div>
  )
}
