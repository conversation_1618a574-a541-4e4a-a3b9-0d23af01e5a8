@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Professional Green Color Palette */
  --primary-50: #f0fdf4;
  --primary-100: #dcfce7;
  --primary-200: #bbf7d0;
  --primary-300: #86efac;
  --primary-400: #4ade80;
  --primary-500: #22c55e;
  --primary-600: #16a34a;
  --primary-700: #15803d;
  --primary-800: #166534;
  --primary-900: #14532d;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */

  /* Border Radius */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  border: 2px solid transparent;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Ensure proper mobile layout */
  body {
    overflow-x: hidden;
  }

  /* Fix mobile navigation issues */
  .mobile-nav-fix {
    position: relative;
    z-index: 60;
  }
}

/* Prevent horizontal scroll on all screen sizes */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Fix for mobile viewport issues */
@media (max-width: 1024px) {
  .mobile-content {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure proper spacing on mobile */
  .mobile-padding-fix {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Fix grid layouts on mobile */
  .mobile-grid-fix {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Additional mobile fixes */
@media (max-width: 768px) {
  /* Ensure cards don't overflow */
  .card-mobile-fix {
    max-width: calc(100vw - 2rem);
    overflow: hidden;
  }

  /* Fix text overflow */
  .text-mobile-fix {
    word-break: break-word;
    overflow-wrap: break-word;
  }
}

/* CSS Custom Properties for Layout */
:root {
  --sidebar-width: 256px;
  --sidebar-collapsed-width: 64px;
  --sidebar-border-width: 1px;
  --content-offset: calc(var(--sidebar-width) + var(--sidebar-border-width));
  --content-offset-collapsed: calc(var(--sidebar-collapsed-width) + var(--sidebar-border-width));
}

/* High Specificity Layout Classes - Override Tailwind */
.dashboard-layout .sidebar-content-alignment {
  margin-left: var(--content-offset) !important;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.dashboard-layout .sidebar-content-alignment-collapsed {
  margin-left: var(--content-offset-collapsed) !important;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

/* Desktop Layout - Force Override */
@media (min-width: 1024px) {
  .dashboard-layout .sidebar-content-alignment {
    margin-left: 257px !important; /* Exact pixel value */
    transform: translateZ(0);
  }

  .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 65px !important; /* Exact pixel value */
    transform: translateZ(0);
  }
}

/* Mobile/Tablet - Reset margins */
@media (max-width: 1023px) {
  .dashboard-layout .sidebar-content-alignment,
  .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 0 !important;
  }
}

/* Navigation Sidebar Exact Dimensions */
.navigation-sidebar {
  width: var(--sidebar-width) !important;
  border-right: var(--sidebar-border-width) solid #e5e7eb !important;
}

.navigation-sidebar.collapsed {
  width: var(--sidebar-collapsed-width) !important;
}

/* Desktop Sidebar Positioning */
@media (min-width: 1024px) {
  .navigation-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    z-index: 30 !important;
  }
}

/* Ensure header spans full width */
.dashboard-layout .sidebar-content-alignment header,
.dashboard-layout .sidebar-content-alignment-collapsed header {
  width: 100% !important;
  position: relative !important;
}

/* Prevent sub-pixel rendering issues */
.dashboard-layout .sidebar-content-alignment,
.dashboard-layout .sidebar-content-alignment-collapsed {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Ultimate Layout Fix - Maximum Specificity */
html body .dashboard-layout .sidebar-content-alignment {
  margin-left: 257px !important;
  padding-left: 0 !important;
  left: 0 !important;
  right: auto !important;
  position: relative !important;
  box-sizing: border-box !important;
}

html body .dashboard-layout .sidebar-content-alignment-collapsed {
  margin-left: 65px !important;
  padding-left: 0 !important;
  left: 0 !important;
  right: auto !important;
  position: relative !important;
  box-sizing: border-box !important;
}

/* Desktop Layout - Main Content Width */
@media (min-width: 1024px) {
  .dashboard-layout .main-content-area {
    width: calc(100% - 257px) !important;
  }

  .dashboard-layout .main-content-area.collapsed {
    width: calc(100% - 65px) !important;
  }
}

/* Mobile Override */
@media (max-width: 1023px) {
  html body .dashboard-layout .sidebar-content-alignment,
  html body .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 0 !important;
  }

  /* Mobile header adjustments */
  .dashboard-layout header {
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  /* Mobile main content adjustments */
  .dashboard-layout .main-content-area {
    margin-left: 0 !important;
    width: 100% !important;
  }

  /* Mobile sidebar positioning */
  .navigation-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    z-index: 50 !important;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .dashboard-layout .main-content-area {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .dashboard-layout .main-content-area {
    max-width: none !important;
  }
}

/* Smooth transitions for all layout changes */
.dashboard-layout * {
  transition-property: margin, padding, width, transform;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent layout shift during transitions */
.dashboard-layout {
  overflow-x: hidden;
}

/* Ensure header stays on top */
.dashboard-layout header {
  z-index: 40 !important;
}

/* Ensure sidebar stays above content but below header */
.navigation-sidebar {
  z-index: 30 !important;
}

/* Prevent content from going under header */
.dashboard-layout .main-content-area {
  min-height: calc(100vh - 80px) !important;
}

/* Professional Typography Classes */
.text-display-large {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.text-display-medium {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

.text-heading-large {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.text-heading-medium {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.text-heading-small {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.text-body-large {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
}

.text-body-medium {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-body-small {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.text-label-large {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-label-medium {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

/* Professional Color Classes */
.text-primary { color: var(--primary-600); }
.text-primary-light { color: var(--primary-500); }
.text-primary-dark { color: var(--primary-700); }

.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-error { color: var(--error-600); }

.text-muted { color: var(--gray-500); }
.text-subtle { color: var(--gray-400); }

.bg-primary { background-color: var(--primary-600); }
.bg-primary-light { background-color: var(--primary-50); }
.bg-success { background-color: var(--success-600); }
.bg-success-light { background-color: var(--success-50); }
.bg-warning { background-color: var(--warning-600); }
.bg-warning-light { background-color: var(--warning-50); }
.bg-error { background-color: var(--error-600); }
.bg-error-light { background-color: var(--error-50); }

/* Professional Card Styles */
.card-elevated {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.card-flat {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.card-flat:hover {
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

/* Professional Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-secondary:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

/* Advanced Animation System */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6); }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-pulse-gentle {
  animation: pulse 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce 1s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Loading Shimmer Effect */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Hover Micro-interactions */
.hover-lift {
  transition: all var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: all var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Interactive Button Effects */
.btn-interactive {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-fast);
}

.btn-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.btn-interactive:hover::before {
  left: 100%;
}

.btn-interactive:active {
  transform: scale(0.98);
}

/* Staggered Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }



/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .debug-layout::before {
    display: none;
  }
}

/* YouTube-Style Dashboard Layout */
.dashboard-layout {
  position: relative;
  overflow-x: hidden;
}

/* Smooth scrolling for the entire layout */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for sidebar */
.navigation-sidebar::-webkit-scrollbar {
  width: 6px;
}

.navigation-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.navigation-sidebar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.navigation-sidebar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.navigation-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 50;
  background: white;
  border-right: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sticky header for sidebar */
.navigation-sidebar .sidebar-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.navigation-sidebar.collapsed {
  width: 65px;
}

.navigation-sidebar:not(.collapsed) {
  width: 257px;
}

.main-content-area {
  position: relative;
  min-height: 100vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Desktop Layout */
@media (min-width: 1024px) {
  .main-content-area {
    margin-left: 257px;
    width: calc(100vw - 257px);
  }

  .main-content-area.collapsed {
    margin-left: 65px;
    width: calc(100vw - 65px);
  }

  .navigation-sidebar {
    position: fixed;
    transform: translateX(0);
  }
}

/* Mobile Layout */
@media (max-width: 1023px) {
  .main-content-area {
    margin-left: 0;
    width: 100vw;
  }

  .navigation-sidebar {
    transform: translateX(-100%);
  }

  .navigation-sidebar.mobile-open {
    transform: translateX(0);
  }
}

/* Header Positioning */
.dashboard-layout header,
.dashboard-header {
  position: fixed;
  top: 0;
  height: 64px;
  z-index: 40;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

@media (min-width: 1024px) {
  .dashboard-layout header,
  .dashboard-header {
    left: 257px;
    width: calc(100vw - 257px);
  }

  .dashboard-layout .navigation-sidebar.collapsed + header,
  .dashboard-header.collapsed {
    left: 65px;
    width: calc(100vw - 65px);
  }
}

@media (max-width: 1023px) {
  .dashboard-layout header,
  .dashboard-header {
    left: 0;
    width: 100vw;
  }
}
